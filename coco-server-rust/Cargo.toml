[package]
name = "coco-server"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = { version = "0.6", features = ["ws"] }
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_yaml = "0.9"
serde_json = "1.0"
tower = "0.4"
tower-http = { version = "0.4", features = ["cors"] }
bytes = "1.0"
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3"
thiserror = "1.0"
anyhow = "1.0"
tokio-rustls = "0.24"
rustls-pemfile = "1.0"
reqwest = { version = "0.11", features = ["json"] }
bcrypt = "0.15"
jsonwebtoken = "9.3"
uuid = { version = "1.17.0", features = ["v4"] }
urlencoding = "2.1.3"
surrealdb = { version = "2.3.7", features = [
    "kv-rocksdb",
    "scripting",
    "protocol-ws",
] }
dashmap = "5.5"
regex = "1.10"
tokio-stream = "0.1"
async-trait = "0.1"
rand = "0.8"
sha2 = "0.10"

[dev-dependencies]
tokio-tungstenite = "0.20"
tempfile = "3.8"
http-body-util = "0.1"
hyper = { version = "0.14", features = ["full"] }
criterion = { version = "0.5", features = ["html_reports"] }
tokio-test = "0.4"

[[bench]]
name = "auth_performance"
harness = false
